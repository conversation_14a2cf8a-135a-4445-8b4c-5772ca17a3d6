# MovieBox API Documentation

## Overview

MovieBox API provides endpoints for movie recommendations, search, and user preferences management. The API is built with FastAPI and uses Supabase for authentication and data storage.

## Authentication

All endpoints require authentication using Supabase bearer tokens. Include the token in the `Authorization` header:

```
Authorization: Bearer <your_supabase_token>
```

To get a token:

1. Register and login through Supabase Auth
2. Use the token provided by Supabase in your API requests

## Rate Limiting

- Free users: 30 requests per day
- Premium users: Unlimited requests

## Endpoints

### Authentication

#### GET /auth/me

Get current user information.

**Response:**

```json
{
  "id": "user_id",
  "email": "<EMAIL>",
  "is_active": true,
  "interests": [
    {
      "en": "Action",
      "ru": "Боевик"
    }
  ],
  "favorite_movies": ["movie_id1", "movie_id2"]
}
```

### Movies

#### GET /movies/search

Search movies by query and filters.

**Query Parameters:**

- `query` (string, required): Search query
- `limit` (integer, optional, default=30): Number of results
- `min_rating` (float, optional, default=0.0): Minimum rating
- `year` (integer, optional): Filter by year
- `genres` (array[string], optional): Filter by genres

**Response:**

```json
[
  {
    "id": "movie_id",
    "title": {
      "en": "Movie Title",
      "ru": "Название фильма"
    },
    "description": {
      "en": "Movie description",
      "ru": "Описание фильма"
    },
    "genres": [
      {
        "en": "Action",
        "ru": "Боевик"
      }
    ],
    "year": 2023,
    "imdb_rating": 8.5,
    "imdb_id": "tt1234567",
    "image_url": "https://image.tmdb.org/t/p/w500/path/to/image.jpg"
  }
]
```

#### GET /movies/popular

Get popular movies.

**Query Parameters:**

- `limit` (integer, optional, default=30): Number of results
- `min_rating` (float, optional, default=0.0): Minimum rating
- `year` (integer, optional): Filter by year

**Response:** Same as search endpoint.

#### GET /movies/{movie_id}

Get movie details by ID.

**Response:** Single movie object.

### Recommendations

#### GET /recommendations

Get movie recommendations based on user preferences.

**Query Parameters:**

- `limit` (integer, optional, default=30): Number of recommendations
- `min_rating` (float, optional, default=7.0): Minimum rating

**Response:** Array of movie objects.

#### GET /recommendations/by-description

Get movie recommendations based on text description.

**Query Parameters:**

- `description` (string, required): Text description
- `limit` (integer, optional, default=30): Number of recommendations
- `min_rating` (float, optional, default=7.0): Minimum rating

**Response:** Array of movie objects.

### User Preferences

#### GET /preferences/me/interests

Get current user's interests.

**Response:**

```json
[
  {
    "en": "Action",
    "ru": "Боевик"
  }
]
```

#### PUT /preferences/me/interests

Update user interests.

**Request Body:**

```json
[
  {
    "en": "Action",
    "ru": "Боевик"
  }
]
```

**Response:** Updated interests array.

#### GET /preferences/me/favorites

Get current user's favorite movies with pagination.

**Query Parameters:**

- `page` (integer, optional, default=0): Page number (0-indexed)
- `size` (integer, optional, default=10, max=100): Number of items per page

**Response:**

```json
{
  "movies": [
    {
      "id": "movie_id",
      "title": {
        "en": "Movie Title",
        "ru": "Название фильма"
      },
      "description": {
        "en": "Movie description",
        "ru": "Описание фильма"
      },
      "genres": [
        {
          "en": "Action",
          "ru": "Боевик"
        }
      ],
      "year": 2023,
      "imdb_rating": 8.5,
      "imdb_id": "tt1234567",
      "image_url": "https://image.tmdb.org/t/p/w500/path/to/image.jpg"
    }
  ],
  "total": 42,
  "has_more": true
}
```

#### POST /preferences/me/favorites/{movie_id}

Add movie to favorites.

**Response:** Updated favorites array.

#### DELETE /preferences/me/favorites/{movie_id}

Remove movie from favorites.

**Response:** Updated favorites array.

### Subscription

#### GET /subscriptions/me

Get current user's subscription.

**Response:**

```json
{
  "id": "subscription_id",
  "user_id": "user_id",
  "is_active": true,
  "plan_type": "premium",
  "expires_at": "2024-12-31T23:59:59Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### POST /subscriptions/activate

Activate subscription.

**Request Body:**

```json
{
  "subscription_id": "subscription_id"
}
```

**Response:** Updated subscription object.

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request

```json
{
  "detail": "Error message"
}
```

### 401 Unauthorized

```json
{
  "detail": "Not authenticated"
}
```

### 403 Forbidden

```json
{
  "detail": "Not enough permissions"
}
```

### 404 Not Found

```json
{
  "detail": "Resource not found"
}
```

### 429 Too Many Requests

```json
{
  "detail": "Daily request limit reached"
}
```

### 500 Internal Server Error

```json
{
  "detail": "Internal server error"
}
```
