from fastapi import APIRouter, Depends, HTTPException, status
from datetime import datetime, timedelta

from ..core.supabase import supabase
from ..models.subscription import Subscription
from ..models.auth import User
from ..services.auth import get_current_active_user
from ..services.subscription import get_user_subscription

router = APIRouter(prefix="/subscriptions", tags=["subscriptions"])

@router.get("/me", response_model=Subscription)
async def get_my_subscription(current_user: User = Depends(get_current_active_user)):
    """Get current user's subscription"""
    return await get_user_subscription(current_user)
