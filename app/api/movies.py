from fastapi import APIRouter, Depends, status
from typing import List

from ..models.movie import Movie
from ..models.auth import User
from ..services.auth import get_current_active_user
from ..services.subscription import check_subscription_limit, increment_request_count
from ..services.movie import movie_service

router = APIRouter(prefix="/movies", tags=["movies"])

@router.get("/recommendations", response_model=List[Movie])
async def get_recommendations(
    limit: int = 20,
    min_rating: float = 7.0,
    current_user: User = Depends(get_current_active_user)
):
    """Get movie recommendations based on user preferences"""
    await check_subscription_limit(current_user)

    recommendations = await movie_service.get_recommendations(
        user_genres=current_user.interests,
        user_favorites=current_user.favorites,
        limit=limit,
        min_rating=min_rating,
        user_id=current_user.id
    )

    await increment_request_count(current_user)

    return recommendations

@router.get("/recommendations-by-description", response_model=List[Movie])
async def get_recommendations_by_description(
    description: str,
    limit: int = 20,
    min_rating: float = 7.0,
    current_user: User = Depends(get_current_active_user)
):
    """Get movie recommendations based on text description"""
    await check_subscription_limit(current_user)

    recommendations = await movie_service.get_recommendations_by_description(
        description=description,
        limit=limit,
        min_rating=min_rating,
    )

    await increment_request_count(current_user)

    return recommendations
