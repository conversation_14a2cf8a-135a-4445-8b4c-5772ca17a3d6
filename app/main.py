from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import logging
import tracemalloc
import time
from typing import Dict, Any

from .api import auth, subscription, movies
from .core.config import get_settings
from .core.middleware import LoggingMiddleware, ErrorHandlingMiddleware
from .services.movie import movie_service
from .services.milvus import milvus_service
from .utils.transformers_patch import apply_transformers_patch

logger = logging.getLogger(__name__)
settings = get_settings()

app = FastAPI(
    title="MovieBox API",
    description="Backend API for MovieBox mobile application",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,
)

# Add custom middleware
app.add_middleware(LoggingMiddleware)
app.add_middleware(ErrorHandlingMiddleware)

# Include routers
app.include_router(auth.router)
app.include_router(subscription.router)
app.include_router(movies.router)

# Store startup metrics
startup_metrics: Dict[str, Any] = {
    "start_time": None,
    "end_time": None,
    "total_time": None,
    "milvus_init_time": None,
    "embedding_gen_time": None,
    "data_load_time": None
}

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    global startup_metrics
    startup_metrics["start_time"] = time.time()
    
    # Enable tracemalloc to track object allocation
    tracemalloc.start()
    
    # Apply patch for transformers library
    apply_transformers_patch()
    
    # Initialize MovieService
    logger.info("[STARTUP] Initializing...")
    
    # Track Milvus initialization time
    milvus_start = time.time()
    milvus_service.initialize()
    startup_metrics["milvus_init_time"] = time.time() - milvus_start
    logger.info(f"[STARTUP] Milvus initialized in {startup_metrics['milvus_init_time']:.2f}s")
    
    # Track data loading time
    data_load_start = time.time()
    await movie_service.initialize()
    startup_metrics["data_load_time"] = time.time() - data_load_start
    logger.info(f"[STARTUP] Data loaded in {startup_metrics['data_load_time']:.2f}s")
    
    # Calculate total startup time
    startup_metrics["end_time"] = time.time()
    startup_metrics["total_time"] = startup_metrics["end_time"] - startup_metrics["start_time"]
    
    logger.info(f"[STARTUP] Initialized successfully in {startup_metrics['total_time']:.2f}s")
    logger.info(f"[STARTUP] Metrics: {startup_metrics}")

@app.get("/health")
async def health_check():
    """Health check endpoint that includes startup metrics."""
    return {
        "status": "healthy",
        "startup_metrics": startup_metrics,
        "background_tasks": movie_service.get_background_task_status()
    }

@app.get("/")
async def root():
    return {"message": "Welcome to MovieBox API"}
