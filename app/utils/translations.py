"""
Utility module for translations between English and Russian.
"""

# Genre translations (English to Russian)
GENRE_TRANSLATIONS = {
    "Action": "Боевик",
    "Adventure": "Приключения",
    "Animation": "Мультфильм",
    "Anime": "Аним<PERSON>",
    "Biography": "Биография",
    "Comedy": "Комедия",
    "Crime": "Крими<PERSON><PERSON>",
    "Documentary": "Документальный",
    "Drama": "Драма",
    "Family": "Семейный",
    "Fantasy": "Фэнтези",
    "Film Noir": "Фильм-нуар",
    "Game Show": "Игровое шоу",
    "History": "Исторический",
    "Horror": "Ужасы",
    "Musical": "Мюзикл",
    "Music": "Музыкальный",
    "Mystery": "Детектив",
    "News": "Новости",
    "Reality-TV": "Реалити-шоу",
    "Reality": "Реалити-шоу",
    "Romance": "Мелодрама",
    "Sci-Fi": "Научная фантастика",
    "Science Fiction": "Научная фантастика",
    "Short": "Короткометражный",
    "Sport": "Спортивный",
    "Talk-Show": "Ток-шоу",
    "Thriller": "Триллер",
    "TV Movie": "Телефильм",
    "War": "Военный",
    "Western": "Вестерн",

    # Alternative spellings and variations
    "Action & Adventure": "Боевик и приключения",
    "Adult": "Для взрослых",
    "Children": "Детский",
    "Children's": "Детский",
    "Kids": "Детский",
    "Classic": "Классика",
    "Disaster": "Фильм-катастрофа",
    "Erotic": "Эротика",
    "Experimental": "Экспериментальный",
    "Exploitation": "Эксплуатационное кино",
    "Faith": "Религиозный",
    "Foreign": "Иностранный",
    "Gay & Lesbian": "ЛГБТ",
    "LGBT": "ЛГБТ",
    "Historical": "Исторический",
    "Holiday": "Праздничный",
    "Independent": "Независимый",
    "Indie": "Инди",
    "Martial Arts": "Боевые искусства",
    "Mockumentary": "Мокьюментари",
    "Neo-noir": "Неонуар",
    "Parody": "Пародия",
    "Political": "Политический",
    "Psychological": "Психологический",
    "Religious": "Религиозный",
    "Road Movie": "Роуд-муви",
    "Romantic Comedy": "Романтическая комедия",
    "Rom-Com": "Романтическая комедия",
    "Satire": "Сатира",
    "Science Fiction & Fantasy": "Научная фантастика и фэнтези",
    "Silent": "Немое кино",
    "Slasher": "Слэшер",
    "Spaghetti Western": "Спагетти-вестерн",
    "Spy": "Шпионский",
    "Superhero": "Супергеройский",
    "Supernatural": "Сверхъестественное",
    "Suspense": "Саспенс",
    "Teen": "Подростковый",
    "War & Politics": "Военный и политика",
    "Zombie": "Зомби"
}

# Country translations (English to Russian)
COUNTRY_TRANSLATIONS = {
    # North America
    "United States of America": "США",
    "USA": "США",
    "United States": "США",
    "U.S.": "США",
    "U.S.A.": "США",
    "Canada": "Канада",
    "Mexico": "Мексика",

    # Central America and Caribbean
    "Antigua and Barbuda": "Антигуа и Барбуда",
    "Bahamas": "Багамы",
    "Barbados": "Барбадос",
    "Belize": "Белиз",
    "Costa Rica": "Коста-Рика",
    "Cuba": "Куба",
    "Dominica": "Доминика",
    "Dominican Republic": "Доминиканская Республика",
    "El Salvador": "Сальвадор",
    "Grenada": "Гренада",
    "Guatemala": "Гватемала",
    "Haiti": "Гаити",
    "Honduras": "Гондурас",
    "Jamaica": "Ямайка",
    "Nicaragua": "Никарагуа",
    "Panama": "Панама",
    "Puerto Rico": "Пуэрто-Рико",
    "Saint Kitts and Nevis": "Сент-Китс и Невис",
    "Saint Lucia": "Сент-Люсия",
    "Saint Vincent and the Grenadines": "Сент-Винсент и Гренадины",
    "Trinidad and Tobago": "Тринидад и Тобаго",

    # South America
    "Argentina": "Аргентина",
    "Bolivia": "Боливия",
    "Brazil": "Бразилия",
    "Chile": "Чили",
    "Colombia": "Колумбия",
    "Ecuador": "Эквадор",
    "Guyana": "Гайана",
    "Paraguay": "Парагвай",
    "Peru": "Перу",
    "Suriname": "Суринам",
    "Uruguay": "Уругвай",
    "Venezuela": "Венесуэла",

    # Western Europe
    "United Kingdom": "Великобритания",
    "UK": "Великобритания",
    "Great Britain": "Великобритания",
    "England": "Англия",
    "Scotland": "Шотландия",
    "Wales": "Уэльс",
    "Northern Ireland": "Северная Ирландия",
    "Ireland": "Ирландия",
    "France": "Франция",
    "Germany": "Германия",
    "Netherlands": "Нидерланды",
    "Holland": "Нидерланды",
    "Belgium": "Бельгия",
    "Luxembourg": "Люксембург",
    "Switzerland": "Швейцария",
    "Austria": "Австрия",
    "Monaco": "Монако",
    "Liechtenstein": "Лихтенштейн",

    # Northern Europe
    "Denmark": "Дания",
    "Finland": "Финляндия",
    "Iceland": "Исландия",
    "Norway": "Норвегия",
    "Sweden": "Швеция",
    "Estonia": "Эстония",
    "Latvia": "Латвия",
    "Lithuania": "Литва",

    # Southern Europe
    "Italy": "Италия",
    "Spain": "Испания",
    "Portugal": "Португалия",
    "Greece": "Греция",
    "Malta": "Мальта",
    "Cyprus": "Кипр",
    "Vatican City": "Ватикан",
    "San Marino": "Сан-Марино",
    "Andorra": "Андорра",

    # Eastern Europe
    "Russia": "Россия",
    "Russian Federation": "Российская Федерация",
    "USSR": "СССР",
    "Soviet Union": "Советский Союз",
    "Ukraine": "Украина",
    "Belarus": "Беларусь",
    "Moldova": "Молдова",
    "Poland": "Польша",
    "Czech Republic": "Чехия",
    "Czechia": "Чехия",
    "Slovakia": "Словакия",
    "Hungary": "Венгрия",
    "Romania": "Румыния",
    "Bulgaria": "Болгария",

    # Balkans
    "Slovenia": "Словения",
    "Croatia": "Хорватия",
    "Bosnia and Herzegovina": "Босния и Герцеговина",
    "Serbia": "Сербия",
    "Montenegro": "Черногория",
    "North Macedonia": "Северная Македония",
    "Macedonia": "Македония",
    "Albania": "Албания",
    "Kosovo": "Косово",

    # Asia - East Asia
    "China": "Китай",
    "Hong Kong": "Гонконг",
    "Taiwan": "Тайвань",
    "Japan": "Япония",
    "South Korea": "Южная Корея",
    "Korea, South": "Южная Корея",
    "North Korea": "Северная Корея",
    "Korea, North": "Северная Корея",
    "Mongolia": "Монголия",

    # Asia - Southeast Asia
    "Thailand": "Таиланд",
    "Vietnam": "Вьетнам",
    "Cambodia": "Камбоджа",
    "Laos": "Лаос",
    "Myanmar": "Мьянма",
    "Burma": "Мьянма",
    "Malaysia": "Малайзия",
    "Singapore": "Сингапур",
    "Indonesia": "Индонезия",
    "Philippines": "Филиппины",
    "Brunei": "Бруней",
    "East Timor": "Восточный Тимор",
    "Timor-Leste": "Восточный Тимор",

    # Asia - South Asia
    "India": "Индия",
    "Pakistan": "Пакистан",
    "Bangladesh": "Бангладеш",
    "Sri Lanka": "Шри-Ланка",
    "Nepal": "Непал",
    "Bhutan": "Бутан",
    "Maldives": "Мальдивы",
    "Afghanistan": "Афганистан",

    # Asia - Central Asia
    "Kazakhstan": "Казахстан",
    "Uzbekistan": "Узбекистан",
    "Turkmenistan": "Туркменистан",
    "Kyrgyzstan": "Киргизия",
    "Tajikistan": "Таджикистан",

    # Asia - Western Asia / Middle East
    "Turkey": "Турция",
    "Iran": "Иран",
    "Iraq": "Ирак",
    "Saudi Arabia": "Саудовская Аравия",
    "Yemen": "Йемен",
    "Oman": "Оман",
    "United Arab Emirates": "Объединенные Арабские Эмираты",
    "UAE": "ОАЭ",
    "Qatar": "Катар",
    "Bahrain": "Бахрейн",
    "Kuwait": "Кувейт",
    "Jordan": "Иордания",
    "Lebanon": "Ливан",
    "Syria": "Сирия",
    "Israel": "Израиль",
    "Palestine": "Палестина",
    "Georgia": "Грузия",
    "Armenia": "Армения",
    "Azerbaijan": "Азербайджан",

    # Oceania
    "Australia": "Австралия",
    "New Zealand": "Новая Зеландия",
    "Papua New Guinea": "Папуа-Новая Гвинея",
    "Fiji": "Фиджи",
    "Solomon Islands": "Соломоновы Острова",
    "Vanuatu": "Вануату",
    "Samoa": "Самоа",
    "Kiribati": "Кирибати",
    "Micronesia": "Микронезия",
    "Tonga": "Тонга",
    "Marshall Islands": "Маршалловы Острова",
    "Palau": "Палау",
    "Tuvalu": "Тувалу",
    "Nauru": "Науру",

    # Africa - North Africa
    "Egypt": "Египет",
    "Libya": "Ливия",
    "Tunisia": "Тунис",
    "Algeria": "Алжир",
    "Morocco": "Марокко",
    "Sudan": "Судан",
    "South Sudan": "Южный Судан",

    # Africa - West Africa
    "Nigeria": "Нигерия",
    "Ghana": "Гана",
    "Ivory Coast": "Кот-д'Ивуар",
    "Côte d'Ivoire": "Кот-д'Ивуар",
    "Senegal": "Сенегал",
    "Guinea": "Гвинея",
    "Mali": "Мали",
    "Burkina Faso": "Буркина-Фасо",
    "Niger": "Нигер",
    "Benin": "Бенин",
    "Togo": "Того",
    "Sierra Leone": "Сьерра-Леоне",
    "Liberia": "Либерия",
    "Guinea-Bissau": "Гвинея-Бисау",
    "Gambia": "Гамбия",
    "Cape Verde": "Кабо-Верде",
    "Mauritania": "Мавритания",

    # Africa - Central Africa
    "Cameroon": "Камерун",
    "Central African Republic": "Центральноафриканская Республика",
    "Chad": "Чад",
    "Democratic Republic of the Congo": "Демократическая Республика Конго",
    "DR Congo": "ДР Конго",
    "Republic of the Congo": "Республика Конго",
    "Congo": "Конго",
    "Gabon": "Габон",
    "Equatorial Guinea": "Экваториальная Гвинея",
    "São Tomé and Príncipe": "Сан-Томе и Принсипи",

    # Africa - East Africa
    "Kenya": "Кения",
    "Tanzania": "Танзания",
    "Uganda": "Уганда",
    "Ethiopia": "Эфиопия",
    "Eritrea": "Эритрея",
    "Somalia": "Сомали",
    "Djibouti": "Джибути",
    "Rwanda": "Руанда",
    "Burundi": "Бурунди",
    "Seychelles": "Сейшельские Острова",
    "Comoros": "Коморы",
    "Mauritius": "Маврикий",
    "Madagascar": "Мадагаскар",

    # Africa - Southern Africa
    "South Africa": "ЮАР",
    "Republic of South Africa": "Южно-Африканская Республика",
    "Namibia": "Намибия",
    "Botswana": "Ботсвана",
    "Zimbabwe": "Зимбабве",
    "Mozambique": "Мозамбик",
    "Zambia": "Замбия",
    "Malawi": "Малави",
    "Angola": "Ангола",
    "Lesotho": "Лесото",
    "Eswatini": "Эсватини",
    "Swaziland": "Свазиленд",

    # Historical countries and regions
    "Yugoslavia": "Югославия",
    "Czechoslovakia": "Чехословакия",
    "East Germany": "Восточная Германия",
    "GDR": "ГДР",
    "West Germany": "Западная Германия",
    "FRG": "ФРГ",
    "Prussia": "Пруссия",
    "Ottoman Empire": "Османская империя",
    "Austro-Hungarian Empire": "Австро-Венгерская империя",
    "Persia": "Персия",
    "Siam": "Сиам",
    "Ceylon": "Цейлон",
    "Rhodesia": "Родезия",
    "Zaire": "Заир",
    "British India": "Британская Индия",
    "Dutch East Indies": "Голландская Ост-Индия",
    "French Indochina": "Французский Индокитай"
}

def translate_genre(genre: str) -> str:
    """
    Translate a genre from English to Russian.

    Args:
        genre: The English genre name

    Returns:
        The Russian translation if available, otherwise the original English name
    """
    return GENRE_TRANSLATIONS.get(genre, genre)

def translate_country(country: str) -> str:
    """
    Translate a country from English to Russian.

    Args:
        country: The English country name

    Returns:
        The Russian translation if available, otherwise the original English name
    """
    return COUNTRY_TRANSLATIONS.get(country, country)
