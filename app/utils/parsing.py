"""
Utility functions for parsing and data conversion.
"""
import logging
import re
import pandas as pd
from typing import List

logger = logging.getLogger(__name__)

def safe_eval_list(list_str: str) -> List[str]:
    """Safely evaluate a string representation of a list with error handling."""
    # Handle nan values
    if pd.isna(list_str):
        return []

    try:
        # Convert to string if not already
        list_str = str(list_str).strip()

        # Handle empty strings
        if not list_str:
            return []

        # Use regex to parse the list instead of eval
        # This is safer and handles apostrophes in strings better
        if list_str.startswith('[') and list_str.endswith(']'):
            # Remove the brackets
            list_str = list_str[1:-1]

        # If the string is empty after removing brackets, return empty list
        if not list_str.strip():
            return []

        # Split by commas, but respect quotes
        items = []
        # Use regex to match items in quotes or separated by commas
        pattern = r'(?:["\']([^"\']*)["\']|([^,]+))'
        matches = re.findall(pattern, list_str)

        for match in matches:
            # Each match is a tuple with either the quoted part or the unquoted part
            item = match[0] if match[0] else match[1]
            item = item.strip()

            # Remove any additional surrounding quotes
            while (item.startswith('"') and item.endswith('"')) or (item.startswith("'") and item.endswith("'")):
                item = item[1:-1]

            if item:  # Only add non-empty items
                items.append(item)

        return items

    except Exception as e:
        logger.error(f"Error parsing list string: {list_str}, Error: {str(e)}")
        # Try to extract items using regex as fallback with a more robust pattern
        try:
            # This pattern should handle apostrophes better
            items = []
            # Remove brackets if present
            if list_str.startswith('[') and list_str.endswith(']'):
                list_str = list_str[1:-1]

            # Split by comma and clean each item
            for item in list_str.split(','):
                item = item.strip()

                # Remove all surrounding quotes if present (including nested quotes)
                while (item.startswith('"') and item.endswith('"')) or (item.startswith("'") and item.endswith("'")):
                    item = item[1:-1]

                if item:
                    items.append(item)

            return items
        except Exception as e2:
            logger.error(f"Fallback list parsing also failed: {str(e2)}")
            return []
