"""
Patch for transformers library to fix FutureWarning about _register_pytree_node.
"""
import logging
import warnings
import sys
import importlib

logger = logging.getLogger(__name__)

def apply_transformers_patch():
    """
    Apply patch to fix FutureWarning in transformers library and handle missing dependencies.
    This function should be called at application startup.
    """
    try:
        # Filter out specific FutureWarning from transformers
        warnings.filterwarnings(
            "ignore",
            message="`torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.",
            category=FutureWarning
        )

        # Patch for huggingface_hub missing function
        try:
            import huggingface_hub
            if not hasattr(huggingface_hub, 'split_torch_state_dict_into_shards'):
                logger.info("Adding missing function to huggingface_hub")
                # Add a dummy implementation of the missing function
                def split_torch_state_dict_into_shards(state_dict, max_shard_size="10GB", weights_name="pytorch_model.bin"):
                    logger.warning("Using dummy implementation of split_torch_state_dict_into_shards")
                    return {"pytorch_model.bin": state_dict}

                huggingface_hub.split_torch_state_dict_into_shards = split_torch_state_dict_into_shards
        except ImportError:
            logger.warning("Could not patch huggingface_hub, it's not installed")

        logger.info("Applied transformers patch to suppress FutureWarning and fix missing dependencies")
    except Exception as e:
        logger.warning(f"Failed to apply transformers patch: {str(e)}")
