from typing import Optional, List
from pydantic import BaseModel, EmailStr
from .movie import Genre, Movie

class UserBase(BaseModel):
    """Base user model"""
    email: EmailStr
    name: Optional[str] = None
    image_url: Optional[str] = None

class User(UserBase):
    """User model for API responses"""
    id: str
    interests: Optional[List[Genre]] = None
    favorites: Optional[List[Movie]] = None
