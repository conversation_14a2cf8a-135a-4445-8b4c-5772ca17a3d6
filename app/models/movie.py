from typing import List, Optional
from pydantic import BaseModel
from .base import Translatable

class Title(Translatable):
    """Movie title in English and Russian"""
    pass

class Description(Translatable):
    """Movie description in English and Russian"""
    pass

class Genre(Translatable):
    """Movie genre in English and Russian"""
    pass

class Country(Translatable):
    """Movie country in English and Russian"""
    pass

class YouTubeId(Translatable):
    """YouTube video ID for English and Russian trailers"""
    pass

class Movie(BaseModel):
    """Movie model matching the Dart structure"""
    id: str
    title: Optional[Title] = None
    description: Optional[Description] = None
    genres: Optional[List[Genre]] = None
    countries: Optional[List[Country]] = None
    year: Optional[int] = None
    imdb_rating: Optional[float] = None
    imdb_id: Optional[str] = None
    kinopoisk_id: Optional[str] = None
    youtube_video_id: Optional[YouTubeId] = None
    image_url: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True
