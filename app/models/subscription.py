from datetime import datetime
from typing import Optional
from pydantic import BaseModel

class SubscriptionBase(BaseModel):
    """Base subscription model"""
    is_active: bool = False
    product_id: str = "free"  # free, premium
    expires_at: Optional[datetime] = None

class SubscriptionCreate(SubscriptionBase):
    """Subscription creation model"""
    user_id: str

class Subscription(SubscriptionBase):
    """Subscription model for API responses"""
    id: str
    user_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True 
