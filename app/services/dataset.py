"""
Dataset service for managing movie dataset downloads and updates.
"""
import logging
import os
import kaggle
from pathlib import Path
from typing import Optional
import pandas as pd
import json

from ..core.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

class DatasetService:
    """Service for managing movie dataset downloads and updates."""

    def __init__(self):
        """Initialize the dataset service."""
        self.dataset_path = Path(settings.MOVIE_DATASET_PATH)
        self.dataset_dir = self.dataset_path.parent
        self.kaggle_dataset = "alanvourch/tmdb-movies-daily-updates"
        self.kaggle_file = "TMDB_all_movies.csv"
        self._setup_kaggle_credentials()

    def _setup_kaggle_credentials(self):
        """Set up Kaggle credentials from environment variables."""
        if not settings.KAGGLE_USERNAME or not settings.KAGGLE_KEY:
            logger.warning("Kaggle credentials not found in environment variables")
            return

        try:
            # Create .kaggle directory if it doesn't exist
            kaggle_dir = Path.home() / ".kaggle"
            kaggle_dir.mkdir(exist_ok=True)

            # Create kaggle.json with credentials
            kaggle_json = {
                "username": settings.KAGGLE_USERNAME,
                "key": settings.KAGGLE_KEY
            }

            kaggle_json_path = kaggle_dir / "kaggle.json"
            with open(kaggle_json_path, "w") as f:
                json.dump(kaggle_json, f)

            # Set appropriate permissions
            os.chmod(kaggle_json_path, 0o600)

            logger.info("Successfully set up Kaggle credentials")
        except Exception as e:
            logger.error(f"Failed to set up Kaggle credentials: {str(e)}")

    def ensure_dataset_exists(self) -> bool:
        """Ensure the movie dataset exists, downloading it if necessary.
        
        Returns:
            bool: True if dataset exists or was successfully downloaded, False otherwise
        """
        if self.dataset_path.exists():
            logger.info(f"Dataset already exists at {self.dataset_path}")
            return True

        try:
            # Create dataset directory if it doesn't exist
            self.dataset_dir.mkdir(parents=True, exist_ok=True)

            # Download dataset from Kaggle
            logger.info(f"Downloading dataset from Kaggle: {self.kaggle_dataset}")
            kaggle.api.dataset_download_file(
                self.kaggle_dataset,
                self.kaggle_file,
                path=str(self.dataset_dir)
            )

            # Verify the download
            if self.dataset_path.exists():
                logger.info(f"Successfully downloaded dataset to {self.dataset_path}")
                return True
            else:
                logger.error("Dataset download completed but file not found")
                return False

        except Exception as e:
            logger.error(f"Failed to download dataset: {str(e)}")
            return False

    def get_dataset_info(self) -> Optional[dict]:
        """Get information about the current dataset.
        
        Returns:
            Optional[dict]: Dictionary containing dataset information or None if dataset doesn't exist
        """
        if not self.dataset_path.exists():
            return None

        try:
            # Read first few rows to get basic info
            df = pd.read_csv(self.dataset_path, nrows=1)
            return {
                "path": str(self.dataset_path),
                "size_mb": self.dataset_path.stat().st_size / (1024 * 1024),
                "columns": list(df.columns),
                "last_modified": self.dataset_path.stat().st_mtime
            }
        except Exception as e:
            logger.error(f"Failed to get dataset info: {str(e)}")
            return None

# Create a singleton instance
dataset_service = DatasetService() 
