import logging
import uuid
from typing import List, Optional
from fastapi import HTTPException, status
from pydantic import BaseModel

from ..core.supabase import supabase
from ..models.movie import Genre, Movie, Country, Description, Title, YouTubeId

logger = logging.getLogger(__name__)

async def get_user_interests(userId: str) -> Optional[List[Genre]]:
    """Get user interests"""
    response = supabase.table("users").select("interests").eq("id", userId).execute()

    if not response.data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return response.data[0].get("interests", [])

async def get_user_favorites(userId: str, size: int = 10) -> Optional[List[Movie]]:
    """Get user favorites - always returns the most recent favorites by size parameter"""
    response = supabase.table("users_favorites").select("*").eq("user_id", userId).order("id", desc=True).limit(size).execute()

    logger.info(f"User favorites response: {response.data}")

    if not response.data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return [convert_to_movie_model(movie) for movie in response.data]

def convert_to_movie_model(movie_data: BaseModel) -> Movie:
    """Convert pandas Series to Movie model with robust error handling"""
    # Extract year from release date if available
    year = None
    try:
        year = int(movie_data['year'])
    except (ValueError, TypeError, IndexError):
        logger.warning(f"Could not parse year from release date: {movie_data.get('release_date')}")
        pass

    # Extract IMDb rating if available
    imdb_rating = None
    try:
        imdb_rating = float(movie_data['imdb_rating'])
    except (ValueError, TypeError):
        logger.warning(f"Could not parse IMDb rating: {movie_data.get('imdb_rating')}")
        pass

    return Movie(
        id=str(movie_data.get('id', uuid.uuid4())),
        title=Title(en=movie_data['title']['en'], ru=movie_data['title']['ru']),
        description=Description(en=movie_data['description']['en'], ru=movie_data['description']['ru']),
        genres=[Genre(en=genre['en'], ru=genre['ru']) for genre in movie_data['genres']],
        countries=[Country(en=country['en'], ru=country['ru']) for country in movie_data['countries']],
        year=year,
        youtube_video_id=YouTubeId(en=movie_data['youtube_video_id']['en'], ru=movie_data['youtube_video_id']['ru']),
        imdb_rating=imdb_rating,
        imdb_id=movie_data.get('imdb_id', None),
        kinopoisk_id=movie_data.get('kinopoisk_id', None),
        image_url=movie_data.get('image_url', None),
    )
