import logging
from typing import Dict, List, Any
from ..core.supabase import supabase

logger = logging.getLogger(__name__)

class ConfigService:
    """Service for managing application configuration from Supabase"""
    
    @staticmethod
    async def get_config() -> Dict[str, Any]:
        """Get configuration from Supabase"""
        try:
            response = supabase.table("config").select("name, value").execute()
            
            if not response.data:
                logger.warning("No configuration found in Supabase")
                return {}
                
            config_dict = {}
            for item in response.data:
                config_dict[item['name']] = item['value']
                
            return config_dict
        except Exception as e:
            logger.error(f"Error fetching configuration from Supabase: {str(e)}")
            return {}
    
    @staticmethod
    async def get_kinopoisk_api_keys() -> List[str]:
        """Get Kinopoisk API keys from Supabase"""
        try:
            config = await ConfigService.get_config()
            
            if 'keys' in config and isinstance(config['keys'], dict) and 'kinopoisk' in config['keys']:
                keys = config['keys']['kinopoisk']
                if isinstance(keys, list):
                    return keys
                    
            logger.warning("No Kinopoisk API keys found in configuration")
            return []
        except Exception as e:
            logger.error(f"Error fetching Kinopoisk API keys: {str(e)}")
            return []


config_service = ConfigService()
