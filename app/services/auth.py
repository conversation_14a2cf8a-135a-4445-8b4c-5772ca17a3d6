from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from ..core.supabase import supabase
from ..models.auth import User
from ..models.movie import Genre
from ..services import user

logger = logging.getLogger(__name__)
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """Get current user from Supabase bearer token"""
    try:
        logger.info(f"Attempting to authenticate user with token: {credentials.credentials[:10]}...")

        user_response = supabase.auth.get_user(credentials.credentials)
        if not user_response or not user_response.user:
            logger.error("No user returned from Supabase auth")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user_id = user_response.user.id
        logger.info(f"Successfully authenticated user: {user_id}")

        supabase.postgrest.auth(credentials.credentials)

        response = supabase.table("users").select("*").eq("id", user_id).execute()
        logger.info(f"User data: {response.data}")
        if not response.data:
            logger.error(f"User {user_id} not found in database")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        favorites_data = await user.get_user_favorites(user_id)

        user_data = response.data[0]
        if 'interests' in user_data and isinstance(user_data['interests'], list):
            user_data['interests'] = [
                Genre(en=interest, ru=interest)
                for interest in user_data['interests']
            ]

        if 'email' not in user_data:
            user_data['email'] = user_response.user.email

        if favorites_data and len(favorites_data) > 0:
            user_data['favorites'] = favorites_data

        logger.info(f"Successfully retrieved user data for: {user_id}")
        return User(**user_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user"""
    if not current_user:
        logger.warning(f"Inactive user attempted to access: {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user"
        )
    return current_user
