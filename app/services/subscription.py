import logging
import uuid
from datetime import datetime
from typing import Optional
from fastapi import Depends, HTTPException, status

from ..core.supabase import supabase
from ..models.subscription import Subscription
from ..models.auth import User
from .auth import get_current_active_user
from ..core.redis import redis_client

logger = logging.getLogger(__name__)

async def get_user_subscription(user: User = Depends(get_current_active_user)) -> Optional[Subscription]:
    """Get user's subscription"""
    response = supabase.table("subscriptions").select("*").eq("user_id", user.id).execute()

    logger.info(f"Subscription response: {response.data}")
    
    if not response.data:
        return Subscription(id=str(uuid.uuid4()), user_id=user.id, is_active=True, product_id="free", expires_at=None, created_at=datetime.now(), updated_at=datetime.now())

    return Subscription(**response.data[0])

async def check_subscription_limit(user: User = Depends(get_current_active_user)) -> bool:
    """Check if user has reached their subscription limit"""
    subscription = await get_user_subscription(user)
    
    if not subscription.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Subscription is not active"
        )
    
    if subscription.product_id == "free":
        # Check daily limit for free users
        key = f"user:{user.id}:daily_requests"
        requests_count = redis_client.get(key)
        
        if requests_count and int(requests_count) >= 30:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Daily request limit reached"
            )
    
    return True

async def increment_request_count(user: User = Depends(get_current_active_user)) -> None:
    """Increment user's daily request count"""
    key = f"user:{user.id}:daily_requests"
    
    # Increment counter and set expiry to end of day
    redis_client.incr(key)
    if redis_client.ttl(key) < 0:  # If key doesn't have expiry
        redis_client.expire(key, 86400)  # 24 hours in seconds 
