"""
External API service for interacting with Kinopoisk and YouTube.
"""
import logging
import requests
import random
import re
import asyncio
import time
from typing import Dict, Optional, Any
from urllib.parse import quote

from ..core.config import get_settings
from ..services.config import config_service

settings = get_settings()
logger = logging.getLogger(__name__)

# Create a global session for connection pooling
http_session = requests.Session()

class ExternalAPIService:
    """Service for interacting with external APIs like Kinopoisk and YouTube."""

    def __init__(self):
        """Initialize the external API service."""
        self.kinopoisk_api_keys = []

    async def refresh_kinopoisk_api_keys(self) -> None:
        """Refresh Kinopoisk API keys from Supabase."""
        try:
            keys = await config_service.get_kinopoisk_api_keys()

            if keys:
                self.kinopoisk_api_keys = keys
                logger.info(f"Refreshed {len(keys)} Kinopoisk API keys from Supabase")
            else:
                logger.warning("No Kinopoisk API keys found in Supabase")
        except Exception as e:
            logger.error(f"Error refreshing Kinopoisk API keys: {str(e)}")

    async def search_kinopoisk(self, title: str) -> Optional[Dict]:
        """Search for a movie on Kinopoisk API."""
        if not title:
            return None

        if not self.kinopoisk_api_keys:
            logger.warning("No Kinopoisk API keys available")
            return None

        try:
            # Select a random API key
            api_key = random.choice(self.kinopoisk_api_keys)

            # Prepare the search URL
            search_url = f"{settings.KINOPOISK_BASE_URL}/api/v2.1/films/search-by-keyword"
            headers = {
                "X-API-KEY": api_key,
                "Content-Type": "application/json"
            }
            params = {
                "keyword": title,
                "page": 1
            }

            # Make the request using the session for connection pooling
            response = http_session.get(search_url, headers=headers, params=params, timeout=5)

            # If we get an unauthorized error, try to refresh the keys
            if response.status_code == 401:
                logger.warning(f"Kinopoisk API key unauthorized, trying to refresh keys")
                await self.refresh_kinopoisk_api_keys()

                if self.kinopoisk_api_keys:
                    api_key = random.choice(self.kinopoisk_api_keys)
                    headers["X-API-KEY"] = api_key
                    response = http_session.get(search_url, headers=headers, params=params, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if data.get('films') and len(data['films']) > 0:
                    result = data['films'][0]
                    return result

            logger.warning(f"Kinopoisk API search failed for title: {title}, status: {response.status_code}")
            return None
        except Exception as e:
            logger.error(f"Error searching Kinopoisk for title {title}: {str(e)}")
            return None

    async def search_youtube_trailer(self, title: str, year: Optional[int] = None, language: str = "en") -> Optional[str]:
        """Search for a movie trailer on YouTube using web scraping (async version)."""
        if not title:
            return None

        try:
            # Prepare the search query
            search_query = f"{title}"
            if year:
                search_query += f" {year}"
            search_query += f" {settings.TRAILER_KEYWORDS.get(language, 'trailer')}"

            # URL encode the search query
            encoded_query = quote(search_query)

            # Prepare the URL
            url = f"{settings.YOUTUBE_SEARCH_BASE_URL}?search_query={encoded_query}"

            # Make the request using asyncio
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            # Use a thread to run the synchronous requests.get with the session
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, lambda: http_session.get(url, headers=headers, timeout=5)
            )

            if response.status_code == 200:
                # Extract video IDs using regex
                trailer_keyword = "trailer" if language == "en" else "трейлер"

                # First look for videoId in the response
                video_id_pattern = r'videoId":"([^"]+)"'
                video_ids = re.findall(video_id_pattern, response.text)

                # Also look for watch URLs
                watch_pattern = r'href="/watch\?v=([^"&]+)"'
                watch_ids = re.findall(watch_pattern, response.text)

                # Combine all found IDs
                all_ids = video_ids + watch_ids

                if all_ids:
                    # Try to find a video with trailer in the title
                    title_pattern = r'title":{"runs":\[{"text":"([^"]+)"}]'
                    titles = re.findall(title_pattern, response.text)

                    # If we have titles, try to match with trailer keyword
                    if titles and len(titles) == len(video_ids):
                        for i, title_text in enumerate(titles):
                            if trailer_keyword.lower() in title_text.lower():
                                result = video_ids[i]
                                return result

                    # If no match or titles not found, return the first ID
                    result = all_ids[0]
                    return result

            logger.warning(f"YouTube scraping failed for title: {title}, status: {response.status_code}")
            return None
        except Exception as e:
            logger.error(f"Error scraping YouTube for title {title}: {str(e)}")
            return None

    async def fetch_movie_data_parallel(self, title: str, year: Optional[int] = None) -> Dict[str, Any]:
        """Fetch movie data from multiple sources in parallel."""
        # Create tasks for all API calls
        tasks = [
            self.search_youtube_trailer(title, year, "en"),
            self.search_youtube_trailer(title, year, "ru"),
            self.search_kinopoisk(title)
        ]

        # Run all tasks concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        logger.debug(f"Parallel API calls completed in {end_time - start_time:.2f}s")

        # Process results
        youtube_en = None
        youtube_ru = None
        kinopoisk_data = None

        # Check each result
        if len(results) >= 3:
            # Handle YouTube EN result
            if not isinstance(results[0], Exception) and results[0]:
                youtube_en = results[0]

            # Handle YouTube RU result
            if not isinstance(results[1], Exception) and results[1]:
                youtube_ru = results[1]

            # Handle Kinopoisk result
            if not isinstance(results[2], Exception) and results[2]:
                kinopoisk_data = results[2]

        # Prepare result dictionary
        result = {
            "youtube_en": youtube_en,
            "youtube_ru": youtube_ru,
            "kinopoisk_data": kinopoisk_data
        }

        return result


# Create a singleton instance
external_api_service = ExternalAPIService()
