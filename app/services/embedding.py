"""
Embedding service for generating and managing embeddings for movies and genres.
Uses local sentence-transformers model (all-MiniLM-L6-v2) for generating high-quality embeddings.
"""
import logging
import numpy as np
from typing import Dict, List, Union, Optional
import time
import gc
import pandas as pd
from sentence_transformers import SentenceTransformer

from ..core.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

class EmbeddingService:
    """Service for generating and managing embeddings for movies and genres."""

    def __init__(self):
        """Initialize the embedding service with local sentence-transformers model."""
        self.model_name = settings.EMBEDDINGS_MODEL
        self.model_revision = settings.EMBEDDINGS_MODEL_REVISION
        self.model: Optional[SentenceTransformer] = None
        self._initialize_model()

    def _initialize_model(self):
        """Initialize the sentence-transformers model."""
        try:
            logger.info(f"Loading local embedding model: {self.model_name}")

            # Load model with or without revision
            if self.model_revision:
                try:
                    self.model = SentenceTransformer(
                        self.model_name,
                        revision=self.model_revision,
                        trust_remote_code=False  # Security: don't execute remote code
                    )
                except Exception as revision_error:
                    logger.warning(f"Failed to load with specific revision, trying without revision: {revision_error}")
                    # Fallback: try without revision
                    self.model = SentenceTransformer(
                        self.model_name,
                        trust_remote_code=False
                    )
            else:
                # Load without revision
                self.model = SentenceTransformer(
                    self.model_name,
                    trust_remote_code=False
                )

            logger.info(f"Successfully loaded model with embedding dimension: {self.model.get_sentence_embedding_dimension()}")

            # Verify the dimension matches our configuration
            actual_dim = self.model.get_sentence_embedding_dimension()
            if actual_dim != settings.EMBEDDING_DIMENSION:
                logger.warning(f"Model dimension ({actual_dim}) doesn't match configured dimension ({settings.EMBEDDING_DIMENSION})")

        except Exception as e:
            logger.error(f"Failed to load embedding model: {str(e)}")
            raise

    def _generate_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """Generate embeddings using local sentence-transformers model."""
        try:
            if self.model is None:
                raise RuntimeError("Embedding model not initialized")

            # Generate embeddings using the local model
            embeddings = self.model.encode(
                texts,
                convert_to_numpy=True,
                normalize_embeddings=True,  # Normalize for better similarity search
                show_progress_bar=len(texts) > 100  # Show progress for large batches
            )

            # Convert to list of numpy arrays with float32 dtype
            return [np.array(embedding, dtype=np.float32) for embedding in embeddings]

        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise

    def generate_overview_embeddings(self, movies_df: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Generate overview embeddings for all movies in parallel, indexed by imdb_id."""
        start_time = time.time()
        embeddings_dict = {}

        # Prepare data for processing
        data = [
            (row['imdb_id'], f"{row['title']}. {row['tagline']}. {row['overview']}")
            for _, row in movies_df.iterrows()
            if pd.notna(row['imdb_id'])
        ]

        batch_size = 1000  # Increased batch size for local processing
        total_movies = len(data)

        for i in range(0, total_movies, batch_size):
            batch = data[i:i+batch_size]
            batch_imdb_ids = [item[0] for item in batch]
            batch_texts = [item[1] for item in batch]

            try:
                logger.info(f"Processing batch {i//batch_size + 1}/{(total_movies + batch_size - 1)//batch_size} with {len(batch_texts)} texts")
                batch_embeddings = self._generate_embeddings(batch_texts)

                # Store results in dictionary
                for j, imdb_id in enumerate(batch_imdb_ids):
                    embedding = batch_embeddings[j]
                    embeddings_dict[imdb_id] = embedding

                # Update progress
                total_processed = min(i + batch_size, total_movies)
                elapsed = time.time() - start_time
                logger.info(f"Processed {total_processed}/{total_movies} movies ({total_processed/total_movies*100:.1f}%) in {elapsed:.1f}s")

                # Force garbage collection after each batch
                gc.collect()

            except Exception as e:
                logger.error(f"Error generating batch embeddings: {str(e)}")
                # Fallback: process items individually
                for imdb_id, overview in batch:
                    try:
                        embedding = self.get_embedding(overview)
                        embeddings_dict[imdb_id] = embedding
                    except Exception:
                        logger.warning(f"Failed to generate embedding for movie {imdb_id}, using zero vector")
                        embeddings_dict[imdb_id] = np.zeros(settings.EMBEDDING_DIMENSION, dtype=np.float32)

        logger.info(f"Generated embeddings for {len(embeddings_dict)} movies in {time.time() - start_time:.1f}s")
        return embeddings_dict

    def get_embedding(self, text: str) -> np.ndarray:
        """Get embedding for a single text using local sentence-transformers model."""
        try:
            embeddings = self._generate_embeddings([text])
            return embeddings[0]
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise

class DummyEmbeddingModel:
    """A dummy model that returns zero vectors when the local model fails."""

    def __init__(self, dimension: int = 384):  # Updated to match all-MiniLM-L6-v2 dimension
        self.dimension = dimension

    def encode(self, texts, **kwargs):
        """Return zero vectors for all texts."""
        if isinstance(texts, str):
            return np.zeros(self.dimension, dtype=np.float32)
        return np.zeros((len(texts), self.dimension), dtype=np.float32)

embedding_service = EmbeddingService()
