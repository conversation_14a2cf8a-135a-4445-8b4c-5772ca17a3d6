"""
Background task service for handling long-running operations in separate threads.
"""
import logging
import threading
import time
import pandas as pd
import numpy as np
from typing import Dict, List, Callable, Optional, Any
import gc

from ..core.config import get_settings
from ..services.cache import cache_service

settings = get_settings()
logger = logging.getLogger(__name__)

class BackgroundTaskService:
    """Service for managing background tasks."""

    def __init__(self):
        """Initialize the background task service."""
        self.active_tasks = {}
        self.lock = threading.Lock()

    def start_task(self, task_name: str, target: Callable, args: tuple = (), kwargs: dict = None) -> bool:
        """Start a new background task if it's not already running.

        Args:
            task_name: Unique identifier for the task
            target: Function to run in the background
            args: Arguments to pass to the target function
            kwargs: Keyword arguments to pass to the target function

        Returns:
            bool: True if task was started, False if it was already running
        """
        with self.lock:
            # Check if task is already running
            if task_name in self.active_tasks and self.active_tasks[task_name].is_alive():
                logger.info(f"Task '{task_name}' is already running")
                return False

            # Create and start the thread
            kwargs = kwargs or {}
            thread = threading.Thread(
                target=self._task_wrapper,
                args=(task_name, target, args, kwargs),
                name=f"bg-{task_name}",
                daemon=True  # Make thread a daemon so it doesn't block app shutdown
            )
            thread.start()

            # Store the thread
            self.active_tasks[task_name] = thread
            logger.info(f"Started background task '{task_name}'")
            return True

    def _task_wrapper(self, task_name: str, target: Callable, args: tuple, kwargs: dict):
        """Wrapper for background tasks to handle exceptions and cleanup."""
        try:
            logger.info(f"Background task '{task_name}' started")
            start_time = time.time()

            # Run the target function
            result = target(*args, **kwargs)

            end_time = time.time()
            logger.info(f"Background task '{task_name}' completed in {end_time - start_time:.2f}s")
            return result
        except Exception as e:
            logger.error(f"Error in background task '{task_name}': {str(e)}", exc_info=True)
        finally:
            # Clean up
            with self.lock:
                if task_name in self.active_tasks:
                    del self.active_tasks[task_name]

            # Force garbage collection
            gc.collect()

    def is_task_running(self, task_name: str) -> bool:
        """Check if a task is currently running.

        Args:
            task_name: The name of the task to check

        Returns:
            bool: True if the task is running, False otherwise
        """
        with self.lock:
            return task_name in self.active_tasks and self.active_tasks[task_name].is_alive()

    def get_running_tasks(self) -> List[str]:
        """Get a list of all currently running tasks.

        Returns:
            List[str]: List of task names that are currently running
        """
        with self.lock:
            return [name for name, thread in self.active_tasks.items() if thread.is_alive()]


def process_remaining_movies_embeddings(
    embedding_service,
    movies_df: pd.DataFrame,
    existing_embeddings: Dict[str, np.ndarray],
    batch_size: int = 500
) -> Dict[str, np.ndarray]:
    """Process embeddings for movies that weren't included in the initial sample.

    Args:
        embedding_service: The embedding service instance to use
        movies_df: DataFrame containing all movies
        existing_embeddings: Dictionary of existing embeddings by imdb_id
        batch_size: Size of batches to process at once

    Returns:
        Dict[str, np.ndarray]: Updated dictionary of embeddings
    """
    logger.info(f"Starting background processing of remaining movies")
    start_time = time.time()

    # Create a copy of existing embeddings to update
    embeddings_dict = existing_embeddings.copy()

    # Get the set of movies that already have embeddings
    processed_imdb_ids = set(embeddings_dict.keys())

    # Prepare data for processing - only include movies that don't have embeddings yet
    data = []
    for _, row in movies_df.iterrows():
        if pd.notna(row['imdb_id']) and row['imdb_id'] not in processed_imdb_ids:
            overview = f"{row['title']}. {row['tagline']}. {row['overview']}"
            data.append((row['imdb_id'], overview))

    total_remaining = len(data)
    if total_remaining == 0:
        logger.info("No remaining movies to process")
        return embeddings_dict

    logger.info(f"Processing embeddings for {total_remaining} remaining movies in batches of {batch_size}")

    # Process in batches
    for i in range(0, total_remaining, batch_size):
        batch = data[i:i+batch_size]
        batch_imdb_ids = [item[0] for item in batch]
        batch_texts = [item[1] for item in batch]

        try:
            # Generate embeddings for this batch
            batch_embeddings = embedding_service._generate_embeddings(batch_texts)

            # Store results in dictionary
            for j, imdb_id in enumerate(batch_imdb_ids):
                embedding = batch_embeddings[j]
                embeddings_dict[imdb_id] = embedding

            # Update progress
            total_processed = min(i + batch_size, total_remaining)
            elapsed = time.time() - start_time
            logger.info(f"Background processed {total_processed}/{total_remaining} movies ({total_processed/total_remaining*100:.1f}%) in {elapsed:.1f}s")

            # Save progress after each batch
            try:
                # Get the current cache data
                cached_data = cache_service.load_cached_embeddings() or {}
                # Update with new embeddings
                cached_data['overview_embeddings'] = embeddings_dict
                # Save back to cache
                cache_service.cache_embeddings(cached_data)
                logger.info(f"Saved {len(embeddings_dict)} embeddings to cache during background processing")
            except Exception as cache_error:
                logger.error(f"Error saving to cache: {str(cache_error)}")

            # Force garbage collection after each batch
            gc.collect()

        except Exception as e:
            logger.error(f"Error generating batch embeddings in background: {str(e)}")
            # Save current progress before continuing
            try:
                cached_data = cache_service.load_cached_embeddings() or {}
                cached_data['overview_embeddings'] = embeddings_dict
                cache_service.cache_embeddings(cached_data)
                logger.info(f"Saved progress after error: {len(embeddings_dict)} embeddings")
            except Exception as cache_error:
                logger.error(f"Error saving progress after error: {str(cache_error)}")
            # Continue with next batch

    total_time = time.time() - start_time
    logger.info(f"Background processing completed: Generated embeddings for {len(embeddings_dict) - len(existing_embeddings)} additional movies in {total_time:.1f}s")

    # Final save to cache
    try:
        cached_data = cache_service.load_cached_embeddings() or {}
        cached_data['overview_embeddings'] = embeddings_dict
        cache_service.cache_embeddings(cached_data)
        logger.info(f"Final save to cache completed: {len(embeddings_dict)} embeddings")
    except Exception as e:
        logger.error(f"Error in final cache save: {str(e)}")

    return embeddings_dict


# Create a singleton instance
background_task_service = BackgroundTaskService()
