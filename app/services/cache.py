"""
Cache service for managing Redis caching and disk caching operations.
"""
import logging
import pickle
from typing import Dict, Set, List, Optional, Any

from ..core.redis import redis_client
from ..core.config import get_settings
from ..models.movie import Movie

settings = get_settings()
logger = logging.getLogger(__name__)

class CacheService:
    """Service for managing Redis caching and disk caching operations."""
    
    def __init__(self):
        """Initialize the cache service."""
        # Ensure cache directory exists
        settings.cache_dir_path.mkdir(parents=True, exist_ok=True)
    
    def load_cached_embeddings(self) -> Optional[Dict[str, Any]]:
        """Load embeddings from cache if available."""
        try:
            cache_file = settings.embeddings_cache_file_path
            if cache_file.exists():
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    logger.info("Loaded embeddings from cache")
                    return cached_data
        except Exception as e:
            logger.error(f"Error loading cached embeddings: {str(e)}")
        return None
    
    def cache_embeddings(self, cache_data: Dict[str, Any]) -> None:
        """Cache embeddings to disk."""
        try:
            cache_file = settings.embeddings_cache_file_path
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            logger.info("Cached embeddings to disk")
        except Exception as e:
            logger.error(f"Error caching embeddings: {str(e)}")
    
    def get_user_history_key(self, user_id: str) -> str:
        """Get the Redis key for a user's recommendation history."""
        return f"user:{user_id}:recommendation_history"
    
    def get_movie_cache_key(self, movie_id: str) -> str:
        """Get the Redis key for a movie cache."""
        return f"movie:{movie_id}:cache"
    
    def save_movie_to_cache(self, movie: Movie) -> None:
        """Save a movie to Redis cache."""
        try:
            # Get the cache key
            key = self.get_movie_cache_key(movie.id)
            
            # Convert movie to JSON
            movie_json = movie.model_dump_json()
            
            # Save to Redis with expiration
            redis_client.setex(key, settings.REDIS_MOVIE_CACHE_TTL, movie_json)
            
            logger.debug(f"Saved movie {movie.id} to cache")
        except Exception as e:
            logger.error(f"Error saving movie {movie.id} to cache: {str(e)}")
    
    def get_movie_from_cache(self, movie_id: str) -> Optional[Movie]:
        """Get a movie from Redis cache."""
        try:
            # Get the cache key
            key = self.get_movie_cache_key(movie_id)
            
            # Get from Redis
            movie_json = redis_client.get(key)
            
            if movie_json:
                # Convert JSON to Movie object
                movie = Movie.model_validate_json(movie_json)
                logger.debug(f"Retrieved movie {movie_id} from cache")
                return movie
            
            return None
        except Exception as e:
            logger.error(f"Error getting movie {movie_id} from cache: {str(e)}")
            return None
    
    def update_recommendation_history(self, user_id: str, movie_ids: List[str]) -> None:
        """Update the recommendation history for a user with new movie IDs."""
        if not user_id or not movie_ids:
            return
        
        key = self.get_user_history_key(user_id)
        
        # Add all new recommendations to the Redis set
        redis_client.sadd(key, *movie_ids)
        
        # Set expiry if not already set
        if redis_client.ttl(key) < 0:
            redis_client.expire(key, settings.REDIS_HISTORY_TTL)
        
        logger.info(f"Added {len(movie_ids)} movies to recommendation history for user {user_id}")
        
        # If history gets too large, trim it
        history_size = redis_client.scard(key)
        if history_size > settings.MAX_HISTORY_SIZE:
            # Get all members
            all_members = list(redis_client.smembers(key))
            # Remove oldest members to keep only MAX_HISTORY_SIZE
            members_to_remove = all_members[:history_size - settings.MAX_HISTORY_SIZE]
            if members_to_remove:
                redis_client.srem(key, *members_to_remove)
                logger.info(f"Trimmed recommendation history for user {user_id} to {settings.MAX_HISTORY_SIZE} items")
    
    def reset_recommendation_history(self, user_id: str) -> None:
        """Reset the recommendation history for a specific user."""
        key = self.get_user_history_key(user_id)
        redis_client.delete(key)
        logger.info(f"Reset recommendation history for user {user_id}")
    
    def get_recommendation_history(self, user_id: str) -> Set[str]:
        """Get the set of previously recommended movie IDs for a user."""
        key = self.get_user_history_key(user_id)
        history = redis_client.smembers(key)
        return history or set()


# Create a singleton instance
cache_service = CacheService()
