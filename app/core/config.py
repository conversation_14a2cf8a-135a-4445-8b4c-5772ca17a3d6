from pydantic_settings import BaseSettings
from functools import lru_cache
from typing import List
from pathlib import Path
import os

class Settings(BaseSettings):
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = False

    # CORS Configuration
    CORS_ORIGINS: List[str] = ["*"]

    # Supabase Configuration
    SUPABASE_URL: str
    SUPABASE_KEY: str = ""  # Default empty string to avoid errors
    SUPABASE_ANON_KEY: str = ""  # Added for compatibility

    # Redis Configuration
    REDIS_URL: str = "redis://redis:6379"
    REDIS_HISTORY_TTL: int = 60 * 60 * 24 * 30  # 30 days in seconds
    REDIS_MOVIE_CACHE_TTL: int = 60 * 60 * 24  # 24 hours in seconds
    REDIS_API_CACHE_TTL: int = 60 * 60 * 24  # 24 hours in seconds
    MAX_HISTORY_SIZE: int = 1000  # Maximum number of items in recommendation history

    # Movie Service Configuration
    MOVIE_DATASET_PATH: str = "data/TMDB_all_movies.csv"
    CACHE_DIR: str = "data/cache"
    EMBEDDINGS_CACHE_FILE: str = "data/cache/embeddings_cache.pkl"
    CHUNK_SIZE: int = 10000
    MAX_WORKERS: int = os.cpu_count() or 4
    EMBEDDING_DIMENSION: int = 384  # Dimension of all-MiniLM-L6-v2 model

    # Local Embeddings Model Configuration
    EMBEDDINGS_MODEL: str = "sentence-transformers/all-MiniLM-L6-v2"
    EMBEDDINGS_MODEL_REVISION: str = ""  # Use latest version for now

    # Kaggle Configuration
    KAGGLE_USERNAME: str = ""
    KAGGLE_KEY: str = ""

    # External APIs
    KINOPOISK_BASE_URL: str = "https://kinopoiskapiunofficial.tech"
    YOUTUBE_SEARCH_BASE_URL: str = "https://www.youtube.com/results"
    TRAILER_KEYWORDS: dict = {
        "en": "official trailer",
        "ru": "русский трейлер"
    }

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Allow extra fields from environment variables

    @property
    def cache_dir_path(self) -> Path:
        """Return the cache directory as a Path object"""
        return Path(self.CACHE_DIR)

    @property
    def embeddings_cache_file_path(self) -> Path:
        """Return the embeddings cache file as a Path object"""
        return Path(self.EMBEDDINGS_CACHE_FILE)

@lru_cache()
def get_settings() -> Settings:
    return Settings()
