import logging
import sys
from typing import Any, Dict
from datetime import datetime
import json
from fastapi import Request
from fastapi.responses import JSONResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('app.log')
    ]
)

logger = logging.getLogger("moviebox")

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        log_data: Dict[str, Any] = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, "extra"):
            log_data.update(record.extra)
        
        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
        
        return json.dumps(log_data)

# Set JSON formatter
json_handler = logging.StreamHandler(sys.stdout)
json_handler.setFormatter(JSONFormatter())
logger.addHandler(json_handler)

async def log_request(request: Request, response: JSONResponse, process_time: float):
    """Log request and response details"""
    log_data = {
        "method": request.method,
        "url": str(request.url),
        "status_code": response.status_code,
        "process_time": f"{process_time:.3f}s",
        "client_ip": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent")
    }
    
    # Add user info if available
    if hasattr(request.state, "user"):
        log_data["user_id"] = request.state.user.id
    
    logger.info("Request processed", extra=log_data)

def log_error(error: Exception, context: Dict[str, Any] = None):
    """Log error with context"""
    log_data = {
        "error_type": error.__class__.__name__,
        "error_message": str(error)
    }
    
    if context:
        log_data.update(context)
    
    logger.error("Error occurred", extra=log_data, exc_info=True) 
