# MovieBox Backend

Backend API for MovieBox application providing personalized movie recommendations.

## Features

- Personalized movie recommendations based on user preferences
- Integration with TMDB dataset
- Supabase authentication and data storage
- Subscription-based access control
- Bilingual support (English/Russian)

## Tech Stack

- FastAPI
- Supabase
- Redis
- Docker
- Snowflake Arctic Embed v2.0 for multilingual NLP recommendations

## Setup

1. Clone the repository
2. Create a `.env` file based on `.env.example`:

   ```
   # Supabase Configuration
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key

   # Redis Configuration
   REDIS_URL=redis://localhost:6379

   # API Configuration
   API_HOST=0.0.0.0
   API_PORT=8000
   DEBUG=True

   # Kaggle Configuration
   KAGGLE_USERNAME=your_kaggle_username
   KAGGLE_KEY=your_kaggle_api_key
   ```

3. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

4. Run with Docker Compose:
   ```bash
   docker-compose up --build
   ```

## API Endpoints

### GET /recommendations

Get personalized movie recommendations based on user preferences.

### POST /recommendations/by-description

Get movie recommendations based on text description.

## Development

1. Install development dependencies:

   ```bash
   pip install -r requirements-dev.txt
   ```

2. Run tests:
   ```bash
   pytest
   ```

## Project Structure

```
app/
├── api/          # API endpoints
├── core/         # Core functionality
├── models/       # Pydantic models
├── services/     # Business logic
└── utils/        # Utility functions
tests/            # Test files
```

## License

MIT
